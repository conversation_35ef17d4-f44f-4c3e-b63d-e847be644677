import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
import numpy as np

def predict_season_winners(matches_df, deliveries_df):
    """
    Predicts IPL season winners based on historical match data.
    This is a simplified model focusing on team strength and basic features.

    Args:
        matches_df (pd.DataFrame): The preprocessed matches DataFrame.
        deliveries_df (pd.DataFrame): The preprocessed deliveries DataFrame.

    Returns:
        None: Prints predictions and model performance.
    """
    print("--- Starting IPL Season Winner Prediction ---")

    if matches_df is None or matches_df.empty or deliveries_df is None or deliveries_df.empty:
        print("Error: Matches or Deliveries DataFrame is empty or not provided. Cannot perform prediction.")
        return

    # --- Feature Engineering ---
    print("\n--- Feature Engineering for Prediction Model ---")

    # Calculate average runs scored by each team per match (from deliveries)
    team_runs_per_match = deliveries_df.groupby(['match_id', 'batting_team'])['total_runs'].sum().reset_index()
    avg_team_runs = team_runs_per_match.groupby('batting_team')['total_runs'].mean().reset_index()
    avg_team_runs.rename(columns={'total_runs': 'avg_runs_per_match'}, inplace=True)
    print("Calculated average runs per match for each team.")

    # Merge match data with team performance metrics
    # We need to create features for both team1 and team2 in a match
    match_data = matches_df.copy()

    # Add a 'winner_encoded' column (1 if team1 wins, 0 if team2 wins)
    # This requires careful handling as 'winner' column contains team names.
    # We'll simplify by considering 'team1' as the home/first team and 'team2' as the away/second team.
    # The model will predict which of these two wins.
    # For a robust model, we'd need to create features for both teams symmetrically.

    # Let's create a simpler target: is team1 the winner?
    match_data['team1_wins'] = (match_data['winner'] == match_data['team1']).astype(int)
    print("Created 'team1_wins' target variable.")

    # Merge average runs for team1
    match_data = pd.merge(
        match_data,
        avg_team_runs.rename(columns={'batting_team': 'team1', 'avg_runs_per_match': 'team1_avg_runs'}),
        on='team1',
        how='left'
    )
    # Merge average runs for team2
    match_data = pd.merge(
        match_data,
        avg_team_runs.rename(columns={'batting_team': 'team2', 'avg_runs_per_match': 'team2_avg_runs'}),
        on='team2',
        how='left'
    )
    match_data.fillna(0, inplace=True) # Fill any NaNs from merging (e.g., new teams)
    print("Merged average runs for team1 and team2.")

    # Basic feature: Home advantage (if 'venue' is available and consistent)
    # This is a simple placeholder; actual home advantage is more complex
    # For now, we'll skip complex venue-based features for simplicity and focus on team strength.

    # Select features and target variable
    # For a simple model, let's use average runs of the participating teams as features
    features = ['team1_avg_runs', 'team2_avg_runs']
    target = 'team1_wins'

    # Filter out matches where winner is unknown or tied (if any)
    match_data_filtered = match_data[match_data['winner'] != 'Unknown'].copy()
    match_data_filtered = match_data_filtered.dropna(subset=features + [target])

    X = match_data_filtered[features]
    y = match_data_filtered[target]

    if X.empty or y.empty:
        print("Error: No sufficient data after feature engineering to train the model.")
        return

    # --- Model Training ---
    print("\n--- Training the Prediction Model ---")
    # Split data into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    # Use RandomForestClassifier for prediction
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)

    print("Model training complete.")

    # --- Model Evaluation ---
    print("\n--- Evaluating Model Performance ---")
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    print(f"Model Accuracy: {accuracy:.2f}")
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred))

    # --- Predicting Upcoming Season Winners (Conceptual) ---
    print("\n--- Conceptual Prediction for Upcoming Season ---")
    print("To predict actual season winners, you would need:")
    print("1. A list of teams participating in the upcoming season.")
    print("2. A mechanism to simulate matches between all teams.")
    print("3. Updated team strength metrics (e.g., current player form, recent match performance).")

    print("\nAs a demonstration, let's predict the winner of a hypothetical match:")
    # Example: Predict a hypothetical match between two teams based on their historical average runs
    team_a = 'Mumbai Indians'
    team_b = 'Chennai Super Kings'

    # Get average runs for hypothetical teams
    avg_runs_a = avg_team_runs[avg_team_runs['batting_team'] == team_a]['avg_runs_per_match'].values
    avg_runs_b = avg_team_runs[avg_team_runs['batting_team'] == team_b]['avg_runs_per_match'].values

    if len(avg_runs_a) > 0 and len(avg_runs_b) > 0:
        hypothetical_match_features = pd.DataFrame([[avg_runs_a[0], avg_runs_b[0]]], columns=features)
        prediction_proba = model.predict_proba(hypothetical_match_features)[0]
        prediction = model.predict(hypothetical_match_features)[0]

        print(f"\nHypothetical Match: {team_a} vs {team_b}")
        if prediction == 1:
            print(f"Predicted Winner: {team_a} (Probability: {prediction_proba[1]:.2f})")
        else:
            print(f"Predicted Winner: {team_b} (Probability: {prediction_proba[0]:.2f})")
    else:
        print(f"Could not find historical average runs for {team_a} or {team_b}. Cannot make hypothetical prediction.")


    print("\n--- IPL Season Winner Prediction Complete ---")

# To run this function, you would first load your data:
# from data_preprocessing_py import load_and_preprocess_ipl_data
# matches_df, deliveries_df = load_and_preprocess_ipl_data('matches.csv', 'deliveries.csv')

# Then call this prediction function:
# if matches_df is not None and deliveries_df is not None:
#     predict_season_winners(matches_df, deliveries_df)
