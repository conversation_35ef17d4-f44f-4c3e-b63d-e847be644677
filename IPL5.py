import pandas as pd
from flask import Flask, request, jsonify
from flask_cors import CORS # Import CORS for handling cross-origin requests
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
import numpy as np
import os

# --- Data Loading and Preprocessing ---
def load_and_preprocess_ipl_data(deliveries_filepath, matches_filepath):
    """
    Loads the IPL matches and deliveries datasets and performs initial preprocessing.
    Handles missing values and converts date column.
    """
    print(f"Attempting to load data from: {matches_filepath} and {deliveries_filepath}")
    try:
        matches_df = pd.read_csv(matches_filepath)
        deliveries_df = pd.read_csv(deliveries_filepath)

        print("Successfully loaded raw datasets.")
        print(f"Columns in matches_df: {matches_df.columns.tolist()}")
        print(f"Columns in deliveries_df: {deliveries_df.columns.tolist()}")


        # Convert 'date' column to datetime objects in matches_df
        if 'date' in matches_df.columns:
            matches_df['date'] = pd.to_datetime(matches_df['date'], errors='coerce')
            print("Converted 'date' column in matches_df to datetime format.")
        else:
            print("Warning: 'date' column not found in matches_df. Date conversion skipped.")

        # Handle missing values: Fill numerical NaNs with 0, categorical with 'Unknown'
        # FIX: Using .loc for direct assignment to avoid FutureWarning with inplace=True
        print("Starting to handle missing values...")
        for col in matches_df.select_dtypes(include=['number']).columns:
            if matches_df[col].isnull().any():
                matches_df.loc[:, col] = matches_df[col].fillna(0)
        for col in matches_df.select_dtypes(include=['object']).columns:
            if matches_df[col].isnull().any():
                matches_df.loc[:, col] = matches_df[col].fillna('Unknown')

        for col in deliveries_df.select_dtypes(include=['number']).columns:
            if deliveries_df[col].isnull().any():
                deliveries_df.loc[:, col] = deliveries_df[col].fillna(0)
        for col in deliveries_df.select_dtypes(include=['object']).columns:
            if deliveries_df[col].isnull().any():
                deliveries_df.loc[:, col] = deliveries_df[col].fillna('Unknown')
        print("Missing value handling complete.")

        return matches_df, deliveries_df

    except FileNotFoundError:
        print(f"Error: One or both files not found. Ensure '{deliveries_filepath}' and '{matches_filepath}' are in the correct directory.")
        return None, None
    except Exception as e:
        print(f"An unexpected error occurred during data loading or preprocessing: {e}")
        return None, None

# --- Player Analysis ---
def analyze_player_performance(deliveries_df):
    """
    Analyzes player performance to identify the greatest batsmen and bowlers.
    Returns dictionaries suitable for JSON serialization.
    """
    print("\n--- Starting Player Performance Analysis ---")
    if deliveries_df is None or deliveries_df.empty:
        print("Error: Deliveries DataFrame is empty or not provided. Cannot perform player analysis.")
        return {}, {}

    # FIX: Ensure 'batting_team' column exists for player analysis
    if 'batting_team' not in deliveries_df.columns:
        if 'batsman_team' in deliveries_df.columns:
            deliveries_df = deliveries_df.rename(columns={'batsman_team': 'batting_team'})
            print("Renamed 'batsman_team' to 'batting_team' in deliveries_df for player analysis.")
        elif 'striker_team' in deliveries_df.columns:
            deliveries_df = deliveries_df.rename(columns={'striker_team': 'batting_team'})
            print("Renamed 'striker_team' to 'batting_team' in deliveries_df for player analysis.")
        else:
            print("Error: 'batting_team' (or common alternatives) not found in deliveries_df. Player analysis might be incomplete.")
            # We will proceed, but some calculations might fail if 'batting_team' is essential.
            # For now, let's ensure it's handled for the groupby below.

    # Analyze Batsmen
    # Ensure 'batting_team' is present for this grouping after potential rename
    if 'batting_team' in deliveries_df.columns:
        batsman_runs = deliveries_df.groupby('batsman')['batsman_runs'].sum().reset_index()
    else:
        print("Warning: 'batting_team' not available for batsman runs calculation. Skipping detailed batsman analysis.")
        batsman_runs = pd.DataFrame(columns=['batsman', 'batsman_runs'])


    # Safely handle 'wide_runs' column
    if 'wide_runs' in deliveries_df.columns:
        balls_faced = deliveries_df[deliveries_df['wide_runs'] == 0].groupby('batsman').size().reset_index(name='balls_faced')
    else:
        print("Warning: 'wide_runs' column not found for accurate balls faced calculation. Assuming all balls faced.")
        balls_faced = deliveries_df.groupby('batsman').size().reset_index(name='balls_faced')

    batsman_stats = pd.merge(batsman_runs, balls_faced, on='batsman', how='left')
    # Calculate strike rate, handle division by zero
    batsman_stats['strike_rate'] = batsman_stats.apply(
        lambda row: (row['batsman_runs'] / row['balls_faced']) * 100 if row['balls_faced'] > 0 else 0,
        axis=1
    )
    batsman_stats.fillna(0, inplace=True) # Fill any remaining NaNs (e.g., from merge)
    min_balls_faced = 50 # Minimum balls faced to be considered
    top_batsmen = batsman_stats[batsman_stats['balls_faced'] >= min_balls_faced].sort_values(by='batsman_runs', ascending=False)

    greatest_batsman = {}
    if not top_batsmen.empty:
        gb = top_batsmen.iloc[0]
        greatest_batsman = {
            "name": gb['batsman'],
            "runs": int(gb['batsman_runs']),
            "strikeRate": round(gb['strike_rate'], 2)
        }
        print(f"Greatest Batsman identified: {greatest_batsman['name']}")
    else:
        print("No top batsmen found with sufficient data.")


    # Analyze Bowlers
    # Safely handle 'player_dismissed' and 'dismissal_kind' columns
    bowler_wickets_count = pd.DataFrame(columns=['bowler', 'wickets'])
    if 'player_dismissed' in deliveries_df.columns and 'dismissal_kind' in deliveries_df.columns:
        bowler_wickets = deliveries_df[
            deliveries_df['player_dismissed'].notna() &
            deliveries_df['dismissal_kind'].isin(['bowled', 'caught', 'lbw', 'stumped', 'caught and bowled', 'hit wicket'])
        ]
        bowler_wickets_count = bowler_wickets.groupby('bowler').size().reset_index(name='wickets')
    else:
        print("Warning: 'player_dismissed' or 'dismissal_kind' column not found for accurate wicket calculation.")

    bowler_runs_conceded = deliveries_df.groupby('bowler')['total_runs'].sum().reset_index(name='runs_conceded')

    # Safely handle 'wide_runs' and 'noball_runs' columns for legal balls
    if 'wide_runs' in deliveries_df.columns and 'noball_runs' in deliveries_df.columns:
        bowler_balls_bowled = deliveries_df[
            (deliveries_df['wide_runs'] == 0) & (deliveries_df['noball_runs'] == 0)
        ].groupby('bowler').size().reset_index(name='legal_balls_bowled')
    else:
        print("Warning: 'wide_runs' or 'noball_runs' column not found for accurate legal balls bowled calculation.")
        bowler_balls_bowled = deliveries_df.groupby('bowler').size().reset_index(name='legal_balls_bowled')


    bowler_stats = pd.merge(bowler_runs_conceded, bowler_wickets_count, on='bowler', how='left')
    bowler_stats = pd.merge(bowler_stats, bowler_balls_bowled, on='bowler', how='left')
    bowler_stats.fillna(0, inplace=True) # Fill NaNs from merges

    bowler_stats['overs_bowled'] = bowler_stats['legal_balls_bowled'] / 6
    # Calculate economy rate, handle division by zero
    bowler_stats['economy_rate'] = bowler_stats.apply(
        lambda row: row['runs_conceded'] / row['overs_bowled'] if row['overs_bowled'] > 0 else 0,
        axis=1
    )
    # Calculate bowling average, handle division by zero
    bowler_stats['bowling_average'] = bowler_stats.apply(
        lambda row: row['runs_conceded'] / row['wickets'] if row['wickets'] > 0 else 0,
        axis=1
    )
    # Calculate bowling strike rate, handle division by zero
    bowler_stats['bowling_strike_rate'] = bowler_stats.apply(
        lambda row: row['legal_balls_bowled'] / row['wickets'] if row['wickets'] > 0 else 0,
        axis=1
    )

    min_wickets = 10 # Minimum wickets to be considered
    min_overs = 10   # Minimum overs to be considered
    top_bowlers = bowler_stats[
        (bowler_stats['wickets'] >= min_wickets) & (bowler_stats['overs_bowled'] >= min_overs)
    ].sort_values(by='wickets', ascending=False)

    greatest_bowler = {}
    if not top_bowlers.empty:
        gb = top_bowlers.iloc[0]
        greatest_bowler = {
            "name": gb['bowler'],
            "wickets": int(gb['wickets']),
            "economyRate": round(gb['economy_rate'], 2)
        }
        print(f"Greatest Bowler identified: {greatest_bowler['name']}")
    else:
        print("No top bowlers found with sufficient data.")

    print("--- Player Performance Analysis Complete ---")
    return greatest_batsman, greatest_bowler

# --- Model Training ---
# Global variables to store trained model and team average runs
model = None
avg_team_runs = None
all_teams_list = []

def train_prediction_model(matches_df, deliveries_df):
    """
    Trains a RandomForestClassifier model for IPL match prediction.
    Populates global variables for the model, average team runs, and all team names.
    """
    global model, avg_team_runs, all_teams_list

    print("\n--- Starting Model Training ---")
    if matches_df is None or matches_df.empty or deliveries_df is None or deliveries_df.empty:
        print("Cannot train model: DataFrames are empty or not provided.")
        return False

    # FIX 1: Rename 'id' column in deliveries_df to 'match_id' if necessary
    if 'match_id' not in deliveries_df.columns and 'id' in deliveries_df.columns:
        deliveries_df = deliveries_df.rename(columns={'id': 'match_id'})
        print("Renamed 'id' column to 'match_id' in deliveries_df for consistency.")
    elif 'match_id' not in deliveries_df.columns and 'id' not in deliveries_df.columns:
        print("Error: Neither 'match_id' nor 'id' column found in deliveries_df. Please check your CSV file headers.")
        return False

    # FIX 2: Ensure 'batting_team' column exists in deliveries_df
    if 'batting_team' not in deliveries_df.columns:
        if 'batsman_team' in deliveries_df.columns:
            deliveries_df = deliveries_df.rename(columns={'batsman_team': 'batting_team'})
            print("Renamed 'batsman_team' to 'batting_team' in deliveries_df for model training.")
        elif 'striker_team' in deliveries_df.columns:
            deliveries_df = deliveries_df.rename(columns={'striker_team': 'batting_team'})
            print("Renamed 'striker_team' to 'batting_team' in deliveries_df for model training.")
        else:
            print("Critical Error: 'batting_team' (or common alternatives like 'batsman_team', 'striker_team') not found in deliveries_df. Cannot proceed with model training.")
            return False


    # Calculate average runs scored by each team per match (from deliveries)
    # Grouping by the now ensured 'match_id' and 'batting_team'
    team_runs_per_match = deliveries_df.groupby(['match_id', 'batting_team'])['total_runs'].sum().reset_index()
    avg_team_runs = team_runs_per_match.groupby('batting_team')['total_runs'].mean().reset_index()
    avg_team_runs.rename(columns={'total_runs': 'avg_runs_per_match'}, inplace=True)
    print("Calculated average runs per match for each team.")

    match_data = matches_df.copy() # Work on a copy to avoid SettingWithCopyWarning
    match_data['team1_wins'] = (match_data['winner'] == match_data['team1']).astype(int)
    print("Created 'team1_wins' target variable.")

    # Merge average runs for team1
    match_data = pd.merge(
        match_data,
        avg_team_runs.rename(columns={'batting_team': 'team1', 'avg_runs_per_match': 'team1_avg_runs'}),
        on='team1',
        how='left'
    )
    # Merge average runs for team2
    match_data = pd.merge(
        match_data,
        avg_team_runs.rename(columns={'batting_team': 'team2', 'avg_runs_per_match': 'team2_avg_runs'}),
        on='team2',
        how='left'
    )
    match_data.fillna(0, inplace=True) # Fill any NaNs from merging (e.g., new teams with no historical avg)
    print("Merged average runs for team1 and team2 into match data.")

    # Define features and target variable
    features = ['team1_avg_runs', 'team2_avg_runs']
    target = 'team1_wins'

    # Filter out matches where winner is unknown or tied (if any) and drop NaNs in features/target
    match_data_filtered = match_data[match_data['winner'] != 'Unknown'].copy()
    match_data_filtered = match_data_filtered.dropna(subset=features + [target])

    X = match_data_filtered[features]
    y = match_data_filtered[target]

    if X.empty or y.empty:
        print("Error: No sufficient data after feature engineering to train the model. Check data quality or feature selection.")
        return False

    # Split data into training and testing sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

    # Initialize and train RandomForestClassifier
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    print("RandomForestClassifier model trained successfully.")

    # Get all unique teams for the frontend dropdowns
    all_teams_list = sorted(list(pd.concat([matches_df['team1'], matches_df['team2']]).unique()))
    print(f"Identified {len(all_teams_list)} unique teams.")

    print("--- Model Training Complete ---")
    return True

# --- Flask App Setup ---
app = Flask(__name__)
CORS(app) # Enable CORS for all routes, crucial for frontend-backend communication

# Global data containers for player stats and model.
# These will be populated once when the Flask app starts.
greatest_batsman_data = {}
greatest_bowler_data = {}

# Load and preprocess data, then train model and analyze players on startup
print("\n--- Initializing Flask App: Loading Data and Training Model ---")
# Ensure the file paths are correct relative to where app.py is run
matches_df_global, deliveries_df_global = load_and_preprocess_ipl_data('matches.csv', 'deliveries.csv')

if matches_df_global is not None and deliveries_df_global is not None:
    # Pass a copy to train_prediction_model to avoid potential SettingWithCopyWarning if it modifies the df
    if train_prediction_model(matches_df_global.copy(), deliveries_df_global.copy()):
        print("Prediction model ready.")
    else:
        print("Prediction model training failed. Check previous logs.")

    # Pass a copy to analyze_player_performance as well
    greatest_batsman_data, greatest_bowler_data = analyze_player_performance(deliveries_df_global.copy())
    print("Player analysis complete.")
else:
    print("Failed to load initial data. API endpoints might not function correctly.")


# --- API Endpoints ---

@app.route('/predict', methods=['POST'])
def predict():
    """
    API endpoint to predict the winner of a hypothetical IPL match.
    Expects JSON payload with 'team1' and 'team2' names.
    """
    data = request.get_json()
    team1_name = data.get('team1')
    team2_name = data.get('team2')

    if not team1_name or not team2_name:
        return jsonify({"error": "Please provide both team1 and team2 names."}), 400

    if model is None or avg_team_runs is None:
        return jsonify({"error": "Prediction model not trained or data not loaded. Server might be initializing or encountered an error."}), 500

    # Retrieve average runs for the selected teams
    avg_runs_team1_series = avg_team_runs[avg_team_runs['batting_team'] == team1_name]['avg_runs_per_match']
    avg_runs_team2_series = avg_team_runs[avg_team_runs['batting_team'] == team2_name]['avg_runs_per_match']

    if avg_runs_team1_series.empty:
        return jsonify({"error": f"Historical data for '{team1_name}' not found. Please check team name or data."}), 404
    if avg_runs_team2_series.empty:
        return jsonify({"error": f"Historical data for '{team2_name}' not found. Please check team name or data."}), 404

    avg_runs_team1 = avg_runs_team1_series.iloc[0]
    avg_runs_team2 = avg_runs_team2_series.iloc[0]

    # Create feature vector for prediction
    # Ensure column names match those used during model training
    hypothetical_match_features = pd.DataFrame([[avg_runs_team1, avg_runs_team2]],
                                                columns=['team1_avg_runs', 'team2_avg_runs'])

    prediction_proba = model.predict_proba(hypothetical_match_features)[0]
    prediction = model.predict(hypothetical_match_features)[0]

    # The model predicts if team1 (as defined by the order in features) wins (1) or not (0)
    predicted_winner = team1_name if prediction == 1 else team2_name
    # Probability of the predicted winner (index 1 for winning, index 0 for not winning)
    probability = prediction_proba[1] if prediction == 1 else prediction_proba[0]

    return jsonify({
        "predicted_winner": predicted_winner,
        "probability": round(probability, 4)
    })

@app.route('/player_stats', methods=['GET'])
def get_player_stats():
    """
    API endpoint to retrieve the greatest batsman and bowler statistics.
    """
    if not greatest_batsman_data or not greatest_bowler_data:
        return jsonify({"error": "Player statistics not available. Server might be initializing or encountered an error."}), 500
    return jsonify({
        "greatestBatsman": greatest_batsman_data,
        "greatestBowler": greatest_bowler_data
    })

@app.route('/teams', methods=['GET'])
def get_teams():
    """
    API endpoint to retrieve a list of all unique IPL team names.
    """
    if not all_teams_list:
        return jsonify({"error": "Team list not available. Server might be initializing or encountered an error."}), 500
    return jsonify({"teams": all_teams_list})


# Run the Flask app
if __name__ == '__main__':
    # This will run the Flask app on http://127.0.0.1:5000/
    # Ensure your frontend fetches from this address.
    # debug=True allows for automatic reloading on code changes and provides detailed error messages.
    app.run(debug=True)
