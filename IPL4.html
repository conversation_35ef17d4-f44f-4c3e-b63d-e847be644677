<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPL Prediction System</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles to enhance Tailwind */
        body {
            font-family: "Inter", sans-serif;
            background-color: #f0f2f5; /* Light gray background */
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .card {
            background-color: #ffffff;
            border-radius: 1rem; /* More rounded corners */
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            padding: 2rem;
            margin-bottom: 1.5rem;
            transition: transform 0.2s ease-in-out;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .gradient-button {
            background-image: linear-gradient(to right, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem; /* Rounded button */
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
        }
        .gradient-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }
        .input-field {
            border: 1px solid #cbd5e0; /* Light gray border */
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            width: 100%;
            box-sizing: border-box; /* Include padding in width */
            transition: border-color 0.2s ease-in-out;
        }
        .input-field:focus {
            outline: none;
            border-color: #6a11cb; /* Purple focus border */
            box-shadow: 0 0 0 3px rgba(106, 17, 203, 0.2); /* Light purple shadow */
        }
        .result-box {
            background-color: #e2e8f0; /* Light blue-gray */
            border-left: 5px solid #2575fc; /* Blue left border */
            padding: 1rem;
            border-radius: 0.5rem;
            margin-top: 1rem;
            font-weight: 600;
            color: #1a202c;
        }
        .error-box {
            background-color: #fee2e2; /* Light red */
            border-left: 5px solid #ef4444; /* Red left border */
            padding: 1rem;
            border-radius: 0.5rem;
            margin-top: 1rem;
            font-weight: 600;
            color: #b91c1c;
        }
        /* Responsive adjustments */
        @media (min-width: 768px) {
            .grid-cols-md-2 {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center py-10">
    <div class="container">
        <h1 class="text-4xl font-extrabold text-center text-gray-800 mb-8">
            <span class="bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-500">
                IPL Prediction System
            </span>
        </h1>

        <!-- Player Analysis Section -->
        <div class="card">
            <h2 class="text-2xl font-bold text-gray-700 mb-4">Greatest Players (2008-2024)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="p-4 bg-blue-50 rounded-lg shadow-inner">
                    <h3 class="text-xl font-semibold text-blue-700 mb-2">Greatest Batsman</h3>
                    <p class="text-gray-800 text-lg">
                        <span id="greatestBatsmanName" class="font-bold">Loading...</span>
                        <br>
                        Total Runs: <span id="greatestBatsmanRuns" class="font-medium">...</span>
                        <br>
                        Strike Rate: <span id="greatestBatsmanSR" class="font-medium">...</span>
                    </p>
                </div>
                <div class="p-4 bg-green-50 rounded-lg shadow-inner">
                    <h3 class="text-xl font-semibold text-green-700 mb-2">Greatest Bowler</h3>
                    <p class="text-gray-800 text-lg">
                        <span id="greatestBowlerName" class="font-bold">Loading...</span>
                        <br>
                        Total Wickets: <span id="greatestBowlerWickets" class="font-medium">...</span>
                        <br>
                        Economy Rate: <span id="greatestBowlerEconomy" class="font-medium">...</span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Match Prediction Section -->
        <div class="card">
            <h2 class="text-2xl font-bold text-gray-700 mb-4">Predict Match Winner</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label for="team1" class="block text-gray-700 text-sm font-bold mb-2">Team 1:</label>
                    <select id="team1" class="input-field">
                        <option value="">Select Team 1</option>
                    </select>
                </div>
                <div>
                    <label for="team2" class="block text-gray-700 text-sm font-bold mb-2">Team 2:</label>
                    <select id="team2" class="input-field">
                        <option value="">Select Team 2</option>
                    </select>
                </div>
            </div>
            <button id="predictButton" class="gradient-button w-full">Predict Winner</button>

            <div id="predictionResult" class="result-box hidden">
                <p class="text-lg font-bold">Predicted Winner: <span id="predictedTeam"></span></p>
                <p class="text-sm">Probability: <span id="predictionProbability"></span></p>
            </div>
             <div id="errorMessage" class="error-box hidden">
                <p class="text-lg font-bold">Error: <span id="errorText"></span></p>
            </div>
            <div id="loadingIndicator" class="hidden text-center mt-4 text-blue-600 font-semibold">
                Predicting... Please wait.
            </div>
        </div>
    </div>

    <script>
        // Define the base URL for your Flask API
        // IMPORTANT: If you are getting 'Failed to fetch' errors, ensure your Flask backend is running
        // and that CORS (Cross-Origin Resource Sharing) is enabled on your Flask server.
        // You might need to install 'flask-cors' (pip install Flask-Cors) and add 'CORS(app)' to your Flask app.
        const API_BASE_URL = 'http://127.0.0.1:5000'; // Make sure this matches your Flask app's address

        // Function to populate team dropdowns from backend
        async function populateTeamDropdowns() {
            const team1Select = document.getElementById('team1');
            const team2Select = document.getElementById('team2');

            try {
                const response = await fetch(`${API_BASE_URL}/teams`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                const allTeams = data.teams;

                allTeams.forEach(team => {
                    const option1 = document.createElement('option');
                    option1.value = team;
                    option1.textContent = team;
                    team1Select.appendChild(option1);

                    const option2 = document.createElement('option');
                    option2.value = team;
                    option2.textContent = team;
                    team2Select.appendChild(option2);
                });
            } catch (error) {
                console.error('Error fetching teams:', error);
                showError('Failed to load teams. Please ensure the backend is running and CORS is configured.');
            }
        }

        // Function to display player stats from backend
        async function displayPlayerStats() {
            try {
                const response = await fetch(`${API_BASE_URL}/player_stats`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();

                document.getElementById('greatestBatsmanName').textContent = data.greatestBatsman.name || 'N/A';
                document.getElementById('greatestBatsmanRuns').textContent = data.greatestBatsman.runs ? `${data.greatestBatsman.runs}` : 'N/A';
                document.getElementById('greatestBatsmanSR').textContent = data.greatestBatsman.strikeRate ? `${data.greatestBatsman.strikeRate}` : 'N/A';

                document.getElementById('greatestBowlerName').textContent = data.greatestBowler.name || 'N/A';
                document.getElementById('greatestBowlerWickets').textContent = data.greatestBowler.wickets ? `${data.greatestBowler.wickets}` : 'N/A';
                document.getElementById('greatestBowlerEconomy').textContent = data.greatestBowler.economyRate ? `${data.greatestBowler.economyRate}` : 'N/A';

            } catch (error) {
                console.error('Error fetching player stats:', error);
                showError('Failed to load player statistics. Please ensure the backend is running and CORS is configured.');
            }
        }

        // Helper function to show errors
        function showError(message) {
            const errorMessageDiv = document.getElementById('errorMessage');
            const errorTextSpan = document.getElementById('errorText');
            errorTextSpan.textContent = message;
            errorMessageDiv.classList.remove('hidden');
        }

        // Helper function to hide messages
        function hideMessages() {
            document.getElementById('predictionResult').classList.add('hidden');
            document.getElementById('errorMessage').classList.add('hidden');
            document.getElementById('loadingIndicator').classList.add('hidden');
        }

        // Event listener for the Predict button
        document.getElementById('predictButton').addEventListener('click', async () => {
            hideMessages(); // Hide previous messages

            const team1 = document.getElementById('team1').value;
            const team2 = document.getElementById('team2').value;
            const predictionResultDiv = document.getElementById('predictionResult');
            const predictedTeamSpan = document.getElementById('predictedTeam');
            const predictionProbabilitySpan = document.getElementById('predictionProbability');
            const loadingIndicator = document.getElementById('loadingIndicator');

            if (!team1 || !team2) {
                showError('Please select both teams for prediction.');
                return;
            }

            if (team1 === team2) {
                showError('Please select two different teams for prediction.');
                return;
            }

            loadingIndicator.classList.remove('hidden'); // Show loading indicator

            try {
                const response = await fetch(`${API_BASE_URL}/predict`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ team1: team1, team2: team2 }),
                });

                loadingIndicator.classList.add('hidden'); // Hide loading indicator

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.predicted_winner) {
                    predictedTeamSpan.textContent = data.predicted_winner;
                    predictionProbabilitySpan.textContent = `${(data.probability * 100).toFixed(2)}%`;
                    predictionResultDiv.classList.remove('hidden');
                } else {
                    showError('Prediction failed: No winner returned.');
                }
            } catch (error) {
                loadingIndicator.classList.add('hidden'); // Hide loading indicator on error
                console.error('Error:', error);
                showError(`An error occurred while fetching prediction: ${error.message}. Please ensure the backend is running and CORS is configured.`);
            }
        });

        // Initialize the page by fetching data from backend
        document.addEventListener('DOMContentLoaded', () => {
            populateTeamDropdowns();
            displayPlayerStats();
        });
    </script>
</body>
</html>
