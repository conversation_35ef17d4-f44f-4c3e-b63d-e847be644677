import pandas as pd

def analyze_player_performance(deliveries_df):
    """
    Analyzes player performance to identify the greatest batsmen and bowlers.

    Args:
        deliveries_df (pd.DataFrame): The preprocessed deliveries DataFrame.

    Returns:
        tuple: A tuple containing:
            - top_batsmen (pd.DataFrame): DataFrame of top batsmen by runs.
            - top_bowlers (pd.DataFrame): DataFrame of top bowlers by wickets.
    """
    print("--- Starting Player Performance Analysis ---")

    if deliveries_df is None or deliveries_df.empty:
        print("Error: Deliveries DataFrame is empty or not provided. Cannot perform analysis.")
        return None, None

    # --- Analyze Batsmen ---
    print("\n--- Analyzing Batsmen ---")
    # Calculate total runs scored by each batsman
    batsman_runs = deliveries_df.groupby('batsman')['batsman_runs'].sum().reset_index()
    batsman_runs = batsman_runs.sort_values(by='batsman_runs', ascending=False)

    # Calculate number of balls faced by each batsman
    # Exclude wide balls from balls faced for strike rate calculation
    balls_faced = deliveries_df[deliveries_df['wide_runs'] == 0].groupby('batsman').size().reset_index(name='balls_faced')

    # Calculate strike rate (runs per 100 balls)
    batsman_stats = pd.merge(batsman_runs, balls_faced, on='batsman', how='left')
    batsman_stats['strike_rate'] = (batsman_stats['batsman_runs'] / batsman_stats['balls_faced']) * 100
    batsman_stats.fillna(0, inplace=True) # Handle cases where balls_faced might be 0

    # Consider only batsmen with a reasonable number of runs/balls faced to avoid skewed strike rates
    min_balls_faced = 50
    top_batsmen = batsman_stats[batsman_stats['balls_faced'] >= min_balls_faced].sort_values(by='batsman_runs', ascending=False)

    print(f"\nTop 10 Batsmen by Total Runs (min {min_balls_faced} balls faced):")
    print(top_batsmen.head(10))

    # --- Analyze Bowlers ---
    print("\n--- Analyzing Bowlers ---")
    # Identify wickets taken by bowlers (excluding run-outs, retired hurt, etc.)
    # Wicket kinds to consider for bowlers: 'bowled', 'caught', 'lbw', 'stumped', 'caught and bowled', 'hit wicket'
    bowler_wickets = deliveries_df[
        deliveries_df['player_dismissed'].notna() &
        deliveries_df['dismissal_kind'].isin(['bowled', 'caught', 'lbw', 'stumped', 'caught and bowled', 'hit wicket'])
    ]
    bowler_wickets_count = bowler_wickets.groupby('bowler').size().reset_index(name='wickets')
    bowler_wickets_count = bowler_wickets_count.sort_values(by='wickets', ascending=False)

    # Calculate runs conceded by each bowler
    bowler_runs_conceded = deliveries_df.groupby('bowler')['total_runs'].sum().reset_index(name='runs_conceded')

    # Calculate balls bowled by each bowler (excluding wide and no-balls for overs calculation)
    # An over is 6 legal balls.
    bowler_balls_bowled = deliveries_df[
        (deliveries_df['wide_runs'] == 0) & (deliveries_df['noball_runs'] == 0)
    ].groupby('bowler').size().reset_index(name='legal_balls_bowled')

    # Merge bowler stats
    bowler_stats = pd.merge(bowler_runs_conceded, bowler_wickets_count, on='bowler', how='left')
    bowler_stats = pd.merge(bowler_stats, bowler_balls_bowled, on='bowler', how='left')
    bowler_stats.fillna(0, inplace=True) # Fill NaN for bowlers with no wickets or no legal balls

    # Calculate overs bowled
    bowler_stats['overs_bowled'] = bowler_stats['legal_balls_bowled'] / 6

    # Calculate economy rate (runs conceded per over)
    # Avoid division by zero for overs_bowled
    bowler_stats['economy_rate'] = bowler_stats.apply(
        lambda row: row['runs_conceded'] / row['overs_bowled'] if row['overs_bowled'] > 0 else 0,
        axis=1
    )

    # Calculate bowling average (runs conceded per wicket)
    # Avoid division by zero for wickets
    bowler_stats['bowling_average'] = bowler_stats.apply(
        lambda row: row['runs_conceded'] / row['wickets'] if row['wickets'] > 0 else 0,
        axis=1
    )

    # Calculate bowling strike rate (balls per wicket)
    # Avoid division by zero for wickets
    bowler_stats['bowling_strike_rate'] = bowler_stats.apply(
        lambda row: row['legal_balls_bowled'] / row['wickets'] if row['wickets'] > 0 else 0,
        axis=1
    )

    # Consider only bowlers with a reasonable number of wickets/overs bowled
    min_wickets = 10
    min_overs = 10
    top_bowlers = bowler_stats[
        (bowler_stats['wickets'] >= min_wickets) & (bowler_stats['overs_bowled'] >= min_overs)
    ].sort_values(by='wickets', ascending=False)

    print(f"\nTop 10 Bowlers by Wickets (min {min_wickets} wickets and {min_overs} overs):")
    print(top_bowlers.head(10))

    print("\n--- Player Performance Analysis Complete ---")
    return top_batsmen, top_bowlers

# To run this function, you would first load your data using the previous script:
# from data_preprocessing_py import load_and_preprocess_ipl_data
# matches_df, deliveries_df = load_and_preprocess_ipl_data('matches.csv', 'deliveries.csv')

# Then call this analysis function:
# if deliveries_df is not None:
#     top_batsmen, top_bowlers = analyze_player_performance(deliveries_df)
